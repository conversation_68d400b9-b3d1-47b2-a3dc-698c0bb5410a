<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MobSkillTemplate;
use App\Models\MobSkill;
use App\Models\ActiveEffect;
use App\Jobs\MobAutoAttackJob;
use App\Services\PlayerHealthService;
use App\Services\BattleLogService;
use App\Services\ObeliskMarkService;
use Illuminate\Support\Facades\Redis;

class TestMobAutoAttackStun extends Command
{
    protected $signature = 'test:mob-auto-attack-stun';
    protected $description = 'Тестирует автоатаку моба с навыком стана';

    public function handle()
    {
        $this->info('🧪 Тестирование автоатаки моба с навыком стана...');

        // Получаем тестового пользователя
        $admin = User::where('name', 'admin')->first();
        if (!$admin) {
            $this->error('❌ Пользователь admin не найден');
            return;
        }

        // Получаем тестового моба (Огр)
        $ogre = Mob::where('name', 'Огр')->first();
        if (!$ogre) {
            $this->error('❌ Моб "Огр" не найден');
            return;
        }

        // Проверяем/создаем шаблон навыка стана
        $template = MobSkillTemplate::where('name', 'Сильный удар стан')->first();
        if (!$template) {
            $template = MobSkillTemplate::create([
                'name' => 'Сильный удар стан',
                'effect_type' => 'stun',
                'effect_data' => [
                    'duration' => 8,
                    'disable_skills' => true,
                    'disable_movement' => true,
                    'disable_actions' => true,
                    'message' => 'Вы оглушены!'
                ],
                'chance' => 100, // 100% для теста
                'cooldown' => 5,
                'duration' => 8,
                'target_type' => 'player',
                'min_health_percent' => 0,
                'max_health_percent' => 100,
                'is_active' => true,
                'priority' => 9,
                'icon' => '⚡'
            ]);
            $this->info("✅ Создан шаблон навыка: {$template->name}");
        }

        // Привязываем навык к мобу
        $mobSkill = MobSkill::where('mob_id', $ogre->id)
            ->where('skill_template_id', $template->id)
            ->first();

        if (!$mobSkill) {
            $mobSkill = MobSkill::create([
                'mob_id' => $ogre->id,
                'skill_template_id' => $template->id,
                'chance' => 100 // 100% для теста
            ]);
            $this->info("✅ Навык привязан к мобу {$ogre->name}");
        }

        // Очищаем старые эффекты
        ActiveEffect::where('target_type', 'player')
            ->where('target_id', $admin->id)
            ->delete();

        // Устанавливаем правильную локацию для игрока
        if (!$admin->statistics) {
            $admin->statistics()->create([
                'current_location' => 'tarnmore_quarry'
            ]);
        } else {
            $admin->statistics->update([
                'current_location' => 'tarnmore_quarry'
            ]);
        }
        $this->info("✅ Установлена локация игрока: tarnmore_quarry");

        // Устанавливаем игрока как онлайн
        $admin->update([
            'is_online' => true,
            'last_activity_timestamp' => now()->timestamp
        ]);
        $this->info("✅ Игрок установлен как онлайн");

        // Создаем метку обелиска для тестирования
        $markService = app(ObeliskMarkService::class);
        $mark = $markService->createMark($admin, $ogre, 'tarnmore_quarry', null, 300);
        $this->info("✅ Создана метка обелиска для игрока (ID: {$mark->id})");

        // Получаем текущее HP игрока
        $playerHealthService = app(PlayerHealthService::class);
        $hpBefore = $playerHealthService->getCurrentHP($admin);
        $this->info("💚 HP игрока до атаки: {$hpBefore}");

        // Очищаем боевые логи
        $battleLogService = app(BattleLogService::class);
        $battleLogKey = $battleLogService->getBattleLogKey($admin->id);
        Redis::del("{$battleLogKey}:logs");

        // Проверяем, что метка действительно активна
        $activeMark = \App\Models\ObeliskMark::where('player_id', $admin->id)
            ->where('location_name', 'tarnmore_quarry')
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->first();

        if ($activeMark) {
            $this->info("✅ Активная метка найдена: ID {$activeMark->id}, истекает " . $activeMark->expires_at->format('Y-m-d H:i:s'));
        } else {
            $this->error("❌ Активная метка НЕ найдена!");
        }

        // Проверяем статистику игрока
        $this->info("📊 Статистика игрока:");
        $this->info("  - Текущая локация: " . ($admin->statistics->current_location ?? 'не установлена'));
        $this->info("  - Онлайн: " . ($admin->is_online ? 'да' : 'нет'));
        $this->info("  - Последняя активность: " . ($admin->last_activity_timestamp ?? 'не установлена'));

        // Проверяем мобов в локации
        $mobsInLocation = \App\Models\Mob::where('location', 'tarnmore_quarry')
            ->orWhere(function ($query) {
                $location = \App\Models\Location::where('name', 'tarnmore_quarry')->first();
                if ($location) {
                    $query->where('location_id', $location->id);
                }
            })
            ->where('hp', '>', 0)
            ->get();

        $this->info("👹 Мобы в локации tarnmore_quarry ({$mobsInLocation->count()}):");
        if ($mobsInLocation->isEmpty()) {
            $this->error("❌ Нет мобов в локации! Создаем тестового моба...");

            // Создаем тестового моба в локации
            $testMob = \App\Models\Mob::create([
                'name' => 'Тестовый Огр',
                'slug' => 'test-ogre-' . time(),
                'location' => 'tarnmore_quarry',
                'hp' => 100,
                'max_hp' => 100,
                'strength' => 50,
                'defense' => 20,
                'experience_reward' => 100,
                'mob_type' => 'normal'
            ]);
            $this->info("✅ Создан тестовый моб: {$testMob->name} (ID: {$testMob->id})");

            // Привязываем навык стана к тестовому мобу
            $mobSkillForTest = \App\Models\MobSkill::create([
                'mob_id' => $testMob->id,
                'skill_template_id' => $template->id,
                'chance' => 100 // 100% для теста
            ]);
            $this->info("✅ Навык стана привязан к тестовому мобу");
        } else {
            foreach ($mobsInLocation as $mob) {
                $this->info("  - {$mob->name} (ID: {$mob->id}, HP: {$mob->hp})");
            }
        }

        // Запускаем MobAutoAttackJob
        $this->info("⚔️ Запускаем автоатаку моба...");
        $job = new MobAutoAttackJob();
        $job->handle(
            $battleLogService,
            app(\App\Services\SkillService::class),
            app(\App\Services\LogFormattingService::class),
            $markService
        );

        // Проверяем HP после атаки
        $hpAfter = $playerHealthService->getCurrentHP($admin);
        $this->info("💚 HP игрока после атаки: {$hpAfter}");

        if ($hpBefore === $hpAfter) {
            $this->info("✅ УСПЕХ: Урон не был нанесен при использовании навыка стана!");
        } else {
            $damage = $hpBefore - $hpAfter;
            if ($damage > 0) {
                $this->error("❌ ОШИБКА: Урон был нанесен! Урон: {$damage}");
            } else {
                $this->info("✅ HP не изменился");
            }
        }

        // Проверяем эффект стана
        $stunEffect = ActiveEffect::where('target_type', 'player')
            ->where('target_id', $admin->id)
            ->where('effect_type', 'stun')
            ->first();

        if ($stunEffect) {
            $this->info("✅ Эффект стана применен:");
            $this->info("  - Длительность: {$stunEffect->duration} сек");
            $this->info("  - Заканчивается: " . $stunEffect->ends_at->format('Y-m-d H:i:s'));
        } else {
            $this->error("❌ Эффект стана НЕ применен!");
        }

        // Проверяем боевые логи
        $logs = $battleLogService->getLogs($battleLogKey);
        $this->info("📜 Боевые логи:");
        if (empty($logs)) {
            $this->info("  - Логи отсутствуют");
        } else {
            foreach (array_slice($logs, 0, 5) as $log) { // Показываем только последние 5 логов
                $message = strip_tags($log['message']); // Убираем HTML теги для консоли
                $this->info("  - {$message}");
            }
        }

        // Очищаем метку после теста
        $markService->deactivateExistingMarks($admin->id, 'tarnmore_quarry');
        $this->info("🧹 Метка обелиска удалена");

        $this->info('🏁 Тест завершен');
    }
}
