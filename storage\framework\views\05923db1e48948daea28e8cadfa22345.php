<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => 'Локация',
    'useCustomImage' => false,
    'customImagePath' => null,
    'size' => 'normal' // normal, small, large
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => 'Локация',
    'useCustomImage' => false,
    'customImagePath' => null,
    'size' => 'normal' // normal, small, large
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Определяем размеры в зависимости от параметра size
    $sizeClasses = match($size) {
        'small' => 'text-xs px-2 py-1 min-h-6',
        'large' => 'text-lg px-6 py-3 min-h-12',
        default => 'text-sm px-4 py-2 min-h-8'
    };
?>



<?php if($useCustomImage && $customImagePath): ?>
    
    <div class="location-name-custom relative inline-block <?php echo e($sizeClasses); ?> text-center font-bold text-white text-shadow-lg rounded-md overflow-hidden">
        
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat rounded-md crisp-rendering"
             style="background-image: url('<?php echo e(asset($customImagePath)); ?>'); background-size: 100% 100%;">
            
            <div class="absolute inset-0 bg-black bg-opacity-30 rounded-md"></div>
        </div>
        
        
        <span class="relative z-10 inline-block"><?php echo e($title); ?></span>
    </div>
<?php else: ?>
    
    <div class="location-name <?php echo e($sizeClasses); ?>"
         style="background-image: url('<?php echo e(asset('assets/UI/locationName.png')); ?>');
                background-position: center center;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                margin: 0 auto;
                color: rgb(136, 141, 75);
                font-weight: 500;
                border-radius: 0.125rem;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 1px 1px 2px rgba(0, 0, 0, 0.9);
                image-rendering: -webkit-optimize-contrast;
                image-rendering: -moz-crisp-edges;
                image-rendering: crisp-edges;
                image-rendering: pixelated;">
        <span style="position: relative; z-index: 2; display: inline-block;"><?php echo e($title); ?></span>
    </div>
<?php endif; ?>

<?php
    // Проверяем флеш-сообщения из Redis для текущего пользователя
    $redisFlashMessage = null;
    if (Auth::check()) {
        try {
            $flashKey = "user_flash_message:" . Auth::id();
            $redisData = \Illuminate\Support\Facades\Redis::get($flashKey);
            
            if ($redisData) {
                $redisFlashMessage = json_decode($redisData, true);
                // Удаляем сообщение после показа
                \Illuminate\Support\Facades\Redis::del($flashKey);
            }
        } catch (\Exception $e) {
            // Игнорируем ошибки Redis
            \Illuminate\Support\Facades\Log::error('Ошибка получения флеш-сообщения из Redis в location-name: ' . $e->getMessage());
        }
    }

    // Определяем классы для разных типов сообщений
    $messageTypes = [
        'success' => [
            'bg' => 'bg-[#36513f]',
            'text' => 'text-[#c8ffdb]',
            'border' => 'border-[#4a7759]',
            'icon' => '✅',
        ],
        'error' => [
            'bg' => 'bg-[#613f36]',
            'text' => 'text-[#ffeac1]',
            'border' => 'border-[#88634a]',
            'icon' => '❌',
        ],
        'warning' => [
            'bg' => 'bg-[#5e553a]',
            'text' => 'text-[#ffe7bd]',
            'border' => 'border-[#7d7248]',
            'icon' => '⚠️',
        ],
        'info' => [
            'bg' => 'bg-[#3a4a5e]',
            'text' => 'text-[#c1dcff]',
            'border' => 'border-[#4a617d]',
            'icon' => 'ℹ️',
        ],
    ];
?>


<?php if (isset($component)) { $__componentOriginal388d947489adfe8fa3e8a456dc1dca6d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal388d947489adfe8fa3e8a456dc1dca6d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.party-invitation-notification','data' => ['compact' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('party-invitation-notification'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['compact' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal388d947489adfe8fa3e8a456dc1dca6d)): ?>
<?php $attributes = $__attributesOriginal388d947489adfe8fa3e8a456dc1dca6d; ?>
<?php unset($__attributesOriginal388d947489adfe8fa3e8a456dc1dca6d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal388d947489adfe8fa3e8a456dc1dca6d)): ?>
<?php $component = $__componentOriginal388d947489adfe8fa3e8a456dc1dca6d; ?>
<?php unset($__componentOriginal388d947489adfe8fa3e8a456dc1dca6d); ?>
<?php endif; ?>


<?php if($redisFlashMessage): ?>
    <?php
        $redisType = $redisFlashMessage['type'] ?? 'info';
        $redisIcon = $redisFlashMessage['icon'] ?? $messageTypes[$redisType]['icon'];
    ?>
    <div class="mt-2 mb-2 mx-2">
        <div
            class="<?php echo e($messageTypes[$redisType]['bg']); ?> <?php echo e($messageTypes[$redisType]['text']); ?> p-2 text-sm rounded border <?php echo e($messageTypes[$redisType]['border']); ?> shadow-inner shadow-black/30 animate-fade-in text-center">
            <div class="flex items-center justify-center">
                <span class="mr-2"><?php echo e($redisIcon); ?></span>
                <span class="font-medium"><?php echo e($redisFlashMessage['message']); ?></span>
            </div>
        </div>
    </div>

    
    <style>
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.3s ease-out forwards;
        }
    </style>
<?php endif; ?>


<?php if (! $__env->hasRenderedOnce('e5e8f81b-aff3-4ae2-b4be-ed0005331eda')): $__env->markAsRenderedOnce('e5e8f81b-aff3-4ae2-b4be-ed0005331eda'); ?>
    <?php $__env->startPush('styles'); ?>
        <style>
            .location-name-custom {
                /* Улучшение четкости изображения */
                image-rendering: -webkit-optimize-contrast;
                image-rendering: -moz-crisp-edges;
                image-rendering: crisp-edges;
                image-rendering: pixelated;
            }

            .crisp-rendering {
                /* Улучшение четкости изображения для всех типов локаций */
                image-rendering: -webkit-optimize-contrast;
                image-rendering: -moz-crisp-edges;
                image-rendering: crisp-edges;
                image-rendering: pixelated;
            }

            .text-shadow-lg {
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }

            /* Дополнительная проверка для стандартного компонента location-name */
            .location-name {
                /* Убеждаемся что позиционирование корректное */
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                margin: 0 auto;
                color: rgb(136, 141, 75);
                font-weight: 500;
                border-radius: 0.125rem;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 1px 1px 2px rgba(0, 0, 0, 0.9);
                min-height: 32px;
                padding: 4px 16px;
                font-size: 0.875rem;
                line-height: 1.25rem;
            }

            .location-name::before {
                /* Убеждаемся что псевдоэлемент корректно позиционирован */
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image: url("<?php echo e(asset('assets/UI/locationName.png')); ?>");
                background-position: center center;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                border-radius: inherit;
                z-index: 0;
                /* Улучшение четкости фонового изображения */
                image-rendering: -webkit-optimize-contrast;
                image-rendering: -moz-crisp-edges;
                image-rendering: crisp-edges;
                image-rendering: pixelated;
            }

            .location-name span {
                /* Убеждаемся что текст поверх фона */
                position: relative;
                z-index: 2;
                display: inline-block;
            }
        </style>
    <?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/location-name.blade.php ENDPATH**/ ?>