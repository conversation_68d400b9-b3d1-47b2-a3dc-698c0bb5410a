<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Mob extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'hp',
        'location',
        'location_id',
        'mine_location_id',
        'max_hp',
        'strength',
        'defense',
        'agility',
        'vitality',
        'intelligence',
        'experience_reward',
        'icon',
        'slug',
        'description',
        'death_time',
        'respawn_time',
        'mob_type'
    ];

    // ИСПРАВЛЕНИЕ: Используем современный способ cast вместо устаревшего $dates
    protected $casts = [
        'death_time' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'mob_type' => 'string'
    ];

    public function resourceDrops()
    {
        return $this->hasMany(ResourceDrop::class, 'mob_id');
    }

    public function skills()
    {
        return $this->hasMany(MobSkill::class, 'mob_id');
    }

    // Связь с активными эффектами
    public function activeEffects()
    {
        return $this->morphMany(ActiveEffect::class, 'target', 'target_type', 'target_id');
    }

    public function getEffectiveLocation()
    {
        if ($this->location_id) {
            $location = Location::find($this->location_id);
            return $location ? $location->name : $this->location;
        }
        return $this->location;
    }

    public function itemDrops()
    {
        return $this->hasMany(ItemDrop::class);
    }

    public function currencyDrops()
    {
        return $this->hasMany(MobCurrencyDrop::class, 'mob_id');
    }

    /**
     * Получить дропы алхимических ингредиентов моба
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function alchemyIngredientDrops()
    {
        return $this->hasMany(AlchemyIngredientDrop::class, 'mob_id');
    }

    public function location()
    {
        return $this->belongsTo(Location::class, 'location_id');
    }

    /**
     * Связь с подлокацией рудника
     */
    public function mineLocation()
    {
        return $this->belongsTo(MineLocation::class, 'mine_location_id');
    }

    // Добавляем геттер для getting_experience, который может брать значение из обоих полей
    public function getGainingExperienceAttribute($value)
    {
        // Если есть прямое значение, используем его
        if (!empty($value)) {
            return $value;
        }

        // Иначе возвращаем значение из поля experience_reward
        return $this->experience_reward ?? 10;
    }

    /**
     * Геттер для получения опыта, выдаваемого за победу над мобом
     *
     * @return int Количество опыта
     */
    public function getExpRewardAttribute()
    {
        // Возвращаем значение из поля experience_reward
        return $this->experience_reward ?? 10;
    }

    // Обновляем метод для тестирования мобов
    public static function testMobs()
    {
        return self::select('id', 'name', 'location_id')->get();
    }

    public function getDroppedItems()
    {
        $drops = [];

        foreach ($this->itemDrops as $drop) {
            $chanceRoll = rand(1, 100); // Случайное число от 1 до 100
            if ($chanceRoll <= $drop->drop_chance) {
                $quantity = rand($drop->min_quantity, $drop->max_quantity); // Количество предметов
                $drops[] = [
                    'item_id' => $drop->item_id,
                    'item' => $drop->item->name,
                    'quantity' => $quantity,
                ];
            }
        }

        return $drops;
    }

    /**
     * Генерирует случайный предметный дроп с моба
     *
     * @return array|null Информация о выпавшем предмете или null, если ничего не выпало
     */
    public function getRandomDroppedItem()
    {
        $drops = [];
        foreach ($this->itemDrops as $drop) {
            $chanceRoll = rand(1, 100); // Случайное число от 1 до 100
            if ($chanceRoll <= $drop->drop_chance) {
                $quantity = rand($drop->min_quantity, $drop->max_quantity); // Количество предметов
                $drops[] = [
                    'item_id' => $drop->item_id,
                    'item' => $drop->item->name,
                    'icon' => $drop->item->icon,
                    'quantity' => $quantity,
                ];
            }
        }

        // Возвращаем один случайный предмет, если что-то выпало, иначе null
        if (!empty($drops)) {
            return $drops[array_rand($drops)]; // Выбираем один случайный дроп
        }
        return null;
    }

    public function getDroppedResources()
    {
        $drops = [];


        foreach ($this->resourceDrops as $drop) {
            $chanceRoll = rand(1, 100); // Случайное число от 1 до 100
            if ($chanceRoll <= $drop->chance) {
                $quantity = rand($drop->min_quantity, $drop->max_quantity); // Количество ресурса
                $drops[] = [
                    'resource' => $drop->resource->name,
                    'quantity' => $quantity,
                ];
            }
        }

        return $drops;
    }

    public function getRandomDroppedCurrency()
    {
        $drops = [];
        foreach ($this->currencyDrops as $drop) {
            $chanceRoll = rand(1, 100); // Случайное число от 1 до 100
            if ($chanceRoll <= $drop->chance) {
                $quantity = rand($drop->min_amount, $drop->max_amount); // Количество валюты
                $drops[] = [
                    'currency' => $drop->currency,
                    'quantity' => $quantity,
                ];
            }
        }

        // Return one random currency if any dropped, otherwise return null
        if (!empty($drops)) {
            return $drops[array_rand($drops)]; // Pick one random drop
        }
        return null;
    }

    public function getRandomDroppedResource()
    {
        $drops = [];
        foreach ($this->resourceDrops as $drop) {
            $chanceRoll = rand(1, 100); // Случайное число от 1 до 100
            if ($chanceRoll <= $drop->chance) {
                $quantity = rand($drop->min_quantity, $drop->max_quantity); // Количество ресурса
                $drops[] = [
                    'resource_id' => $drop->resource_id,
                    'resource' => $drop->resource->name,
                    'icon' => $drop->resource->icon,
                    'quantity' => $quantity,
                ];
            }
        }

        // Return one random resource if any dropped, otherwise return null
        if (!empty($drops)) {
            return $drops[array_rand($drops)]; // Pick one random drop
        }
        return null;
    }

    /**
     * Обрабатывает специальные эффекты от умений моба
     *
     * @param \App\Models\User $user Цель атаки
     * @param string $skillName Название умения
     * @return void
     */
    public function processSkillEffects($user, $skillName)
    {
        // Логирование для отладки
        \Log::info('Обработка эффекта умения моба:', [
            'mob_id' => $this->id,
            'mob_name' => $this->name,
            'skill_name' => $skillName,
            'user_id' => $user->id
        ]);

        // Сохраняем ID последнего атакующего моба в сессии
        session(['last_attacking_mob_id' => $this->id]);

        // Проверка на умение "Сильный удар"
        if ($skillName === 'Сильный удар') {
            // Вероятность применения эффекта стана (например, 70%)
            $stunChance = 70;

            if (rand(1, 100) <= $stunChance) {
                // Получаем ID умения стана
                $stunSkillId = 14; // Изменено на 14 в соответствии с шаблоном
                $stunDuration = 5; // Продолжительность в секундах

                // Применяем эффект стана
                $effect = \App\Models\ActiveEffect::applyStun($user, $stunDuration, $stunSkillId);

                // Получаем и используем сервис логов боя, если он доступен
                $battleLogKey = "location:tarnmore_quarry:{$user->id}";
                $battleLogService = app(\App\Services\BattleLogService::class);

                if ($battleLogService) {
                    $battleLogService->addLog(
                        $battleLogKey,
                        "{$this->name} оглушает Вас на {$stunDuration} секунд!",
                        'warning'
                    );
                } else {
                    // Резервный вариант - добавление в сессию
                    session()->push('battle_logs', [
                        'type' => 'warning',
                        'message' => "{$this->name} оглушает Вас на {$stunDuration} секунд!",
                        'timestamp' => now()
                    ]);
                }

                \Log::info('Применен эффект оглушения от умения моба:', [
                    'mob_name' => $this->name,
                    'skill_name' => $skillName,
                    'user_id' => $user->id,
                    'duration' => $stunDuration,
                    'effect_id' => $effect->id
                ]);
            }
        }
    }

    /**
     * Получить случайный алхимический ингредиент из дропа
     *
     * @return array|null Информация о выпавшем ингредиенте или null, если ничего не выпало
     */
    public function getRandomDroppedAlchemyIngredient()
    {
        $drops = [];
        foreach ($this->alchemyIngredientDrops as $drop) {
            $chanceRoll = rand(1, 100); // Случайное число от 1 до 100
            if ($chanceRoll <= $drop->drop_chance) {
                $quantity = rand($drop->min_quantity, $drop->max_quantity); // Количество ингредиентов
                $drops[] = [
                    'alchemy_ingredient_id' => $drop->alchemy_ingredient_id,
                    'ingredient' => $drop->alchemyIngredient->name,
                    'icon' => $drop->alchemyIngredient->getIconPathAttribute(),
                    'quantity' => $quantity,
                ];
            }
        }

        // Возвращаем один случайный ингредиент, если что-то выпало, иначе null
        if (!empty($drops)) {
            return $drops[array_rand($drops)]; // Выбираем один случайный дроп
        }
        return null;
    }
}
