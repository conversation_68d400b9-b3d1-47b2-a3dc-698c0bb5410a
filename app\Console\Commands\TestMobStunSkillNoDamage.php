<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MobSkillTemplate;
use App\Models\MobSkill;
use App\Models\ActiveEffect;
use App\Services\MobSkillFramework;
use App\Services\PlayerHealthService;
use App\Services\BattleLogService;
use Illuminate\Support\Facades\Redis;

class TestMobStunSkillNoDamage extends Command
{
    protected $signature = 'test:mob-stun-no-damage';
    protected $description = 'Тестирует навык стана моба без нанесения урона';

    public function handle()
    {
        $this->info('🧪 Тестирование навыка стана моба без урона...');

        // Получаем тестового пользователя
        $admin = User::where('name', 'admin')->first();
        if (!$admin) {
            $this->error('❌ Пользователь admin не найден');
            return;
        }

        // Получаем тестового моба (Огр)
        $ogre = Mob::where('name', 'Огр')->first();
        if (!$ogre) {
            $this->error('❌ Моб "Огр" не найден');
            return;
        }

        // Проверяем/создаем шаблон навыка стана
        $template = MobSkillTemplate::where('name', 'Сильный удар стан')->first();
        if (!$template) {
            $template = MobSkillTemplate::create([
                'name' => 'Сильный удар стан',
                'effect_type' => 'stun',
                'effect_data' => [
                    'duration' => 5,
                    'disable_skills' => true,
                    'disable_movement' => true,
                    'disable_actions' => true,
                    'message' => 'Вы оглушены!'
                ],
                'chance' => 100, // 100% для теста
                'cooldown' => 10,
                'duration' => 5,
                'target_type' => 'player',
                'min_health_percent' => 0,
                'max_health_percent' => 100,
                'is_active' => true,
                'priority' => 8,
                'icon' => '⚡'
            ]);
            $this->info("✅ Создан шаблон навыка: {$template->name}");
        }

        // Привязываем навык к мобу
        $mobSkill = MobSkill::where('mob_id', $ogre->id)
            ->where('skill_template_id', $template->id)
            ->first();

        if (!$mobSkill) {
            $mobSkill = MobSkill::create([
                'mob_id' => $ogre->id,
                'skill_template_id' => $template->id,
                'chance' => 100 // 100% для теста
            ]);
            $this->info("✅ Навык привязан к мобу {$ogre->name}");
        }

        // Очищаем старые эффекты
        ActiveEffect::where('target_type', 'player')
            ->where('target_id', $admin->id)
            ->delete();

        // Получаем текущее HP игрока
        $playerHealthService = app(PlayerHealthService::class);
        $hpBefore = $playerHealthService->getCurrentHP($admin);
        $this->info("💚 HP игрока до атаки: {$hpBefore}");

        // Очищаем боевые логи
        $battleLogService = app(BattleLogService::class);
        $battleLogKey = "location:test:{$admin->id}";
        Redis::del("{$battleLogKey}:logs");

        // Тестируем навык через MobSkillFramework
        $skillFramework = app(MobSkillFramework::class);
        $results = $skillFramework->processMobSkills($ogre, $admin, [
            'event_type' => 'attack',
            'location_key' => 'test',
            'test_mode' => true
        ]);

        $this->info("⚔️ Результаты активации навыков:");
        if (empty($results)) {
            $this->info("  - Навыки не активированы");
        } else {
            foreach ($results as $result) {
                $skillName = $result['skill_name'] ?? 'Неизвестный навык';
                $skillType = $result['skill_type'] ?? 'unknown';
                $this->info("  - Навык: {$skillName} (тип: {$skillType})");
                $this->info("  - Успех: " . (($result['success'] ?? false) ? 'Да' : 'Нет'));
                if (isset($result['message'])) {
                    $this->info("  - Сообщение: {$result['message']}");
                }
                // Отладочная информация
                $this->info("  - Полная структура: " . json_encode($result, JSON_UNESCAPED_UNICODE));
            }
        }

        // Проверяем HP после атаки
        $hpAfter = $playerHealthService->getCurrentHP($admin);
        $this->info("💚 HP игрока после атаки: {$hpAfter}");

        if ($hpBefore === $hpAfter) {
            $this->info("✅ УСПЕХ: Урон не был нанесен!");
        } else {
            $this->error("❌ ОШИБКА: Урон был нанесен! Разница: " . ($hpBefore - $hpAfter));
        }

        // Проверяем эффект стана
        $stunEffect = ActiveEffect::where('target_type', 'player')
            ->where('target_id', $admin->id)
            ->where('effect_type', 'stun')
            ->first();

        if ($stunEffect) {
            $this->info("✅ Эффект стана применен:");
            $this->info("  - Длительность: {$stunEffect->duration} сек");
            $this->info("  - Заканчивается: " . $stunEffect->ends_at->format('Y-m-d H:i:s'));
        } else {
            $this->error("❌ Эффект стана НЕ применен!");
        }

        // Проверяем боевые логи
        $logs = $battleLogService->getLogs($battleLogKey);
        $this->info("📜 Боевые логи:");
        foreach ($logs as $log) {
            $this->info("  - {$log['message']}");
        }

        $this->info('🏁 Тест завершен');
    }
}
