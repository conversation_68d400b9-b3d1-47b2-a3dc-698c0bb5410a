<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['resourcesInLocation' => [], 'user' => null, 'routePrefix' => 'battle.mines.custom', 'isStunned' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['resourcesInLocation' => [], 'user' => null, 'routePrefix' => 'battle.mines.custom', 'isStunned' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div class="flex justify-center items-center py-0 px-2 gap-1.5">
    <?php $__empty_1 = true; $__currentLoopData = $resourcesInLocation; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $res): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <?php
            // Проверяем, выбран ли данный ресурс
            $isSelected = $user->current_target_id == $res->id && $user->current_target_type == 'resource';
        ?>
        <form action="<?php echo e(route($routePrefix . '.select-resource', [request()->route('slug'), $res->id])); ?>" method="POST"
            class="flex-shrink-0">
            <?php echo csrf_field(); ?>
            <button type="submit" <?php echo e($isStunned ? 'disabled' : ''); ?> class="relative w-11 h-11 bg-gradient-to-b from-[#5a4d36] to-[#453b2a]
                                    border-2 <?php echo e($isSelected ? 'border-[#e9d5a0] resource-glow' : 'border-[#a6925e]'); ?>

                                    <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:brightness-125 hover:shadow-[0_0_5px_#a6925e70]'); ?>

                                    transition-all duration-300 rounded-md overflow-hidden">

                
                <img src="<?php echo e($res->resource->icon_path); ?>" alt="<?php echo e($res->resource->name ?? 'Ресурс'); ?>"
                    class="w-full h-full object-cover p-0.5 <?php echo e($isSelected ? 'animate-pulse' : ''); ?>"
                    onerror="this.src='<?php echo e(asset('assets/resources/EtherealOrb.png')); ?>'">

                
                <?php if(isset($res->durability)): ?>
                    <?php
                        // Определяем цвет в зависимости от прочности
                        $durabilityColor =
                            $res->durability > 70
                            ? '#4CAF50'
                            : ($res->durability > 30
                                ? '#FFC107'
                                : '#F44336');
                    ?>

                    
                    <div class="absolute bottom-0 left-0 right-0 h-1.5 bg-[#1a1915] opacity-70">
                        <div class="h-full <?php echo e($isSelected ? 'animate-pulse' : ''); ?>"
                            style="width: <?php echo e($res->durability); ?>%; background-color: <?php echo e($durabilityColor); ?>">
                        </div>
                    </div>
                <?php endif; ?>
            </button>
        </form>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="text-center text-[#a6925e] text-sm py-2">
            Ресурсы отсутствуют в этой локации.
        </div>
    <?php endif; ?>
</div>

<style>
    @keyframes resourcePulse {
        0% {
            box-shadow: 0 0 2px 1px rgba(233, 213, 160, 0.3);
        }

        50% {
            box-shadow: 0 0 8px 3px rgba(233, 213, 160, 0.7);
        }

        100% {
            box-shadow: 0 0 2px 1px rgba(233, 213, 160, 0.3);
        }
    }

    .resource-glow {
        animation: resourcePulse 2s infinite ease-in-out;
    }
</style><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/mines/resource-block.blade.php ENDPATH**/ ?>