<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['breadcrumbs' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['breadcrumbs' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="breadcrumbs relative flex items-center">
  <!-- фон -->
  <div class="absolute inset-0 w-full h-[25px]" style="
    background-image: url('<?php echo e(asset('assets/UI/bread-crumbs.png')); ?>');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  ">
  </div>

  <!-- навигация -->
  <nav class="relative z-10 flex items-center h-full text-xs font-serif w-full pl-3" style="margin-top: -4.1px;">
    <?php $__currentLoopData = $breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $breadcrumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <?php if($loop->first): ?>
      
      <a href="<?php echo e($breadcrumb['url']); ?>" class="flex items-center text-[#d9c89b] hover:text-[#ffba70]
        transition drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)] leading-none">
        <!-- домик -->
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-[#e5b769]" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
        <polyline points="9 22 9 12 15 12 15 22" />
        </svg>
        <span class="relative -top-[-2px]"><?php echo e($breadcrumb['label']); ?></span>
      </a>

      <?php elseif($loop->last): ?>
      <span class="flex items-center text-[#adadad]
         drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)] leading-none">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mx-1 text-[#a6925e]" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2">
        <polyline points="9 18 15 12 9 6" />
        </svg>
        <span class="relative -top-[-2px]"><?php echo e($breadcrumb['label']); ?></span>
      </span>

      <?php else: ?>
      
      <a href="<?php echo e($breadcrumb['url']); ?>" class="flex items-center text-[#d9c89b] hover:text-[#ffba70]
        transition drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)] leading-none">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mx-1 text-[#a6925e]" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2">
        <polyline points="9 18 15 12 9 6" />
        </svg>
        <span class="relative -top-[-2px]"><?php echo e($breadcrumb['label']); ?></span>
      </a>
      <?php endif; ?>
  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  </nav>
</div>

<style>
  .breadcrumbs {
    height: 25px;
    position: relative;
    overflow: hidden;

  }

  .breadcrumbs nav {
    /* сохраняем ваш хак для иконки */
    margin-top: -4.1px;
  }
</style><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/breadcrumbs.blade.php ENDPATH**/ ?>