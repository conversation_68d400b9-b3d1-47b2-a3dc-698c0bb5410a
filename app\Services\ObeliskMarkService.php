<?php

namespace App\Services;

use App\Models\ObeliskMark;
use App\Models\User;
use App\Models\Mob;
use App\Models\ActiveEffect;
use App\Events\ObeliskMarkCreated;
use App\Events\ObeliskMarkExpired;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ObeliskMarkService
{
    /**
     * Создать метку обелиска для игрока
     *
     * @param User $player Игрок
     * @param Mob|null $mob Моб, который выбрал игрока (УСТАРЕЛО: теперь мобы выбираются случайно)
     * @param string $locationName Название локации
     * @param int|null $locationId ID локации
     * @param int $duration Длительность метки в секундах
     * @return ObeliskMark
     */
    public function createMark(
        User $player,
        ?Mob $mob,
        string $locationName,
        ?int $locationId = null,
        int $duration = 60
    ): ObeliskMark {
        DB::beginTransaction();

        try {
            // Деактивируем существующие метки игрока в этой локации
            $this->deactivateExistingMarks($player->id, $locationName);

            // Создаем новую метку в БД
            $mark = ObeliskMark::createMark(
                $player->id,
                $mob?->id,
                $locationName,
                $locationId,
                $duration
            );

            // Создаем соответствующий ActiveEffect для совместимости
            $this->createActiveEffect($player, $mob, $duration);

            DB::commit();

            // Запускаем событие создания метки
            event(new ObeliskMarkCreated($mark));

            Log::info('Создана метка обелиска', [
                'mark_id' => $mark->id,
                'player_id' => $player->id,
                'player_name' => $player->name,
                'mob_id' => $mob?->id,
                'mob_name' => $mob?->name,
                'location' => $locationName,
                'duration' => $duration,
                'expires_at' => $mark->expires_at,
                'random_mob_attacks' => true // Новая система случайных атак
            ]);

            return $mark;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Ошибка при создании метки обелиска', [
                'player_id' => $player->id,
                'mob_id' => $mob?->id,
                'location' => $locationName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Удалить существующие метки игрока в локации
     * Полностью удаляет метки для предотвращения конфликтов уникальности
     *
     * @param int $playerId ID игрока
     * @param string $locationName Название локации
     * @return int Количество удаленных меток
     */
    public function deactivateExistingMarks(int $playerId, string $locationName): int
    {
        // Получаем все метки игрока в локации
        $existingMarks = ObeliskMark::where('player_id', $playerId)
            ->where('location_name', $locationName)
            ->get();

        $deletedCount = 0;

        foreach ($existingMarks as $mark) {
            // Удаляем из кэша
            $mark->removeFromCache();

            // Запускаем событие истечения если метка была активна
            if ($mark->is_active) {
                event(new ObeliskMarkExpired($mark));
            }

            // Полностью удаляем метку
            $mark->delete();
            $deletedCount++;
        }

        // Также удаляем соответствующие ActiveEffect
        ActiveEffect::where('target_type', 'player')
            ->where('target_id', $playerId)
            ->where('skill_id', 12) // ID умения "Метка обелиска"
            ->delete();

        return $deletedCount;
    }

    /**
     * Создать ActiveEffect для совместимости со старой системой
     *
     * @param User $player Игрок
     * @param Mob $mob Моб
     * @param int $duration Длительность
     * @return ActiveEffect
     */
    private function createActiveEffect(User $player, Mob $mob, int $duration): ActiveEffect
    {
        return ActiveEffect::create([
            'target_type' => 'player',
            'target_id' => $player->id,
            'skill_id' => 12, // ID умения "Метка обелиска"
            'caster_type' => 'mob',
            'caster_id' => $mob->id,
            'duration' => $duration,
            'ends_at' => now()->addSeconds($duration),
            'effect_data' => ['damage' => 10],
        ]);
    }

    /**
     * Проверить, есть ли у игрока активная метка в локации
     * Включает мгновенную проверку истечения и автоматическую очистку
     *
     * @param int $playerId ID игрока
     * @param string $locationName Название локации
     * @return bool
     */
    public function hasActiveMark(int $playerId, string $locationName): bool
    {
        try {
            // Проактивная очистка истекших меток в локации перед проверкой
            $this->proactiveCleanupExpiredMarks($locationName);

            // Проверяем в БД с точными условиями активности
            $mark = ObeliskMark::where('player_id', $playerId)
                ->where('location_name', $locationName)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->first();

            if ($mark) {
                Log::info('Найдена активная метка обелиска', [
                    'player_id' => $playerId,
                    'location_name' => $locationName,
                    'mark_id' => $mark->id,
                    'expires_at' => $mark->expires_at,
                    'is_active' => $mark->is_active
                ]);

                // Кэшируем активную метку
                $mark->cacheInRedisWithPreciseTTL();
                return true;
            }

            Log::debug('Активная метка обелиска не найдена', [
                'player_id' => $playerId,
                'location_name' => $locationName
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error('Ошибка при проверке активной метки обелиска', [
                'player_id' => $playerId,
                'location_name' => $locationName,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Получить активную метку игрока в локации
     *
     * @param int $playerId ID игрока
     * @param string $locationName Название локации
     * @return ObeliskMark|null
     */
    public function getActiveMark(int $playerId, string $locationName): ?ObeliskMark
    {
        return ObeliskMark::where('player_id', $playerId)
            ->where('location_name', $locationName)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->first();
    }

    /**
     * Получить всех игроков с активными метками в локации
     *
     * @param string $locationName Название локации
     * @return array
     */
    public function getMarkedPlayersInLocation(string $locationName): array
    {
        // Сначала пытаемся получить из Redis кэша
        $cachedPlayers = ObeliskMark::getActivePlayersInLocation($locationName);

        if (!empty($cachedPlayers)) {
            return $cachedPlayers;
        }

        // Если нет в кэше, получаем из БД
        $marks = ObeliskMark::where('location_name', $locationName)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->with(['player', 'mob'])
            ->get();

        $players = [];
        foreach ($marks as $mark) {
            // Кэшируем каждую метку
            $mark->cacheInRedis();

            $players[] = [
                'id' => $mark->id,
                'player_id' => $mark->player_id,
                'mob_id' => $mark->mob_id,
                'location_name' => $mark->location_name,
                'expires_at' => $mark->expires_at->timestamp,
                'is_active' => $mark->is_active,
                'last_attack_at' => $mark->last_attack_at?->timestamp,
                'attack_count' => $mark->attack_count ?? 0,
            ];
        }

        return $players;
    }

    /**
     * Обновить время последней атаки для метки
     *
     * @param int $playerId ID игрока
     * @param string $locationName Название локации
     * @return bool
     */
    public function updateLastAttack(int $playerId, string $locationName): bool
    {
        $mark = $this->getActiveMark($playerId, $locationName);

        if ($mark) {
            return $mark->updateLastAttack();
        }

        return false;
    }

    /**
     * Очистить истекшие метки
     *
     * @return int Количество удаленных меток
     */
    public function cleanupExpiredMarks(): int
    {
        $count = ObeliskMark::cleanupExpired();

        Log::info('Очищены истекшие метки обелисков', [
            'cleaned_count' => $count
        ]);

        return $count;
    }

    /**
     * Получить статистику меток по локациям
     *
     * @return array
     */
    public function getMarkStatistics(): array
    {
        $stats = ObeliskMark::selectRaw('
                location_name,
                COUNT(*) as total_marks,
                COUNT(CASE WHEN is_active = true AND expires_at > NOW() THEN 1 END) as active_marks,
                AVG(attack_count) as avg_attacks
            ')
            ->groupBy('location_name')
            ->get()
            ->toArray();

        return $stats;
    }

    /**
     * Принудительно деактивировать метку
     *
     * @param int $markId ID метки
     * @return bool
     */
    public function forceDeactivateMark(int $markId): bool
    {
        $mark = ObeliskMark::find($markId);

        if ($mark && $mark->is_active) {
            $result = $mark->deactivate();

            if ($result) {
                event(new ObeliskMarkExpired($mark));

                Log::info('Принудительно деактивирована метка обелиска', [
                    'mark_id' => $markId,
                    'player_id' => $mark->player_id,
                    'location' => $mark->location_name
                ]);
            }

            return $result;
        }

        return false;
    }

    /**
     * Очистить истекшую метку конкретного игрока
     * Мгновенная очистка для предотвращения ложных срабатываний
     *
     * @param int $playerId ID игрока
     * @param string $locationName Название локации
     * @return bool
     */
    public function cleanupExpiredMarkForPlayer(int $playerId, string $locationName): bool
    {
        try {
            // Находим все метки игрока в локации (активные и неактивные)
            $marks = ObeliskMark::where('player_id', $playerId)
                ->where('location_name', $locationName)
                ->get();

            $deletedCount = 0;
            foreach ($marks as $mark) {
                // Удаляем из Redis кэша
                $mark->removeFromCache();

                // Запускаем событие истечения метки если она была активна
                if ($mark->is_active) {
                    event(new ObeliskMarkExpired($mark));
                }

                // Полностью удаляем метку из БД
                $mark->delete();
                $deletedCount++;
            }

            // Удаляем связанные ActiveEffect
            ActiveEffect::where('target_type', 'App\\Models\\User')
                ->where('target_id', $playerId)
                ->where('skill_id', 12) // ID умения метки обелиска
                ->delete();

            if ($deletedCount > 0) {
                Log::info('Мгновенная очистка меток обелиска', [
                    'player_id' => $playerId,
                    'location_name' => $locationName,
                    'deleted_count' => $deletedCount
                ]);
            }

            return $deletedCount > 0;
        } catch (\Exception $e) {
            Log::error('Ошибка при мгновенной очистке метки обелиска', [
                'player_id' => $playerId,
                'location_name' => $locationName,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Проактивная очистка всех истекших меток в локации
     * Вызывается при каждом обращении к системе меток
     *
     * @param string $locationName Название локации
     * @return int Количество очищенных меток
     */
    public function proactiveCleanupExpiredMarks(string $locationName): int
    {
        try {
            $expiredMarks = ObeliskMark::where('location_name', $locationName)
                ->where(function ($query) {
                    $query->where('expires_at', '<', now())
                        ->orWhere('is_active', false);
                })
                ->get();

            $cleanedCount = 0;
            foreach ($expiredMarks as $mark) {
                // Удаляем из кэша
                $mark->removeFromCache();

                // Запускаем событие истечения если метка была активна
                if ($mark->is_active) {
                    event(new ObeliskMarkExpired($mark));
                }

                // Полностью удаляем метку
                $mark->delete();
                $cleanedCount++;
            }

            if ($cleanedCount > 0) {
                Log::info('Проактивная очистка истекших меток', [
                    'location_name' => $locationName,
                    'cleaned_count' => $cleanedCount
                ]);
            }

            return $cleanedCount;
        } catch (\Exception $e) {
            Log::error('Ошибка при проактивной очистке меток', [
                'location_name' => $locationName,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }
}
